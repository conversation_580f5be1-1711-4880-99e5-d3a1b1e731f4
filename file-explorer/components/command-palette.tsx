"use client"

import { useState, useEffect } from "react"
import { Command } from "lucide-react"
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Input } from "@/components/ui/input"

type CommandItem = {
  id: string
  name: string
  description: string
  shortcut?: string[]
  category: string
  action?: () => void
}

export default function CommandPalette() {
  const [open, setOpen] = useState(false)
  const [search, setSearch] = useState("")
  const [commandItems, setCommandItems] = useState<CommandItem[]>([])
  const [filteredItems, setFilteredItems] = useState<CommandItem[]>([])

  // Initialize command items
  useEffect(() => {
    const items: CommandItem[] = [
      {
        id: "new-file",
        name: "New File",
        description: "Create a new file",
        shortcut: ["Ctrl", "N"],
        category: "File",
        action: () => console.log("New file"),
      },
      {
        id: "open-file",
        name: "Open File",
        description: "Open a file",
        shortcut: ["Ctrl", "O"],
        category: "File",
        action: () => console.log("Open file"),
      },
      {
        id: "save-file",
        name: "Save File",
        description: "Save current file",
        shortcut: ["Ctrl", "S"],
        category: "File",
        action: () => console.log("Save file"),
      },
      {
        id: "format-document",
        name: "Format Document",
        description: "Format the current document",
        shortcut: ["Shift", "Alt", "F"],
        category: "Edit",
        action: () => console.log("Format document"),
      },
      {
        id: "find",
        name: "Find",
        description: "Find in current file",
        shortcut: ["Ctrl", "F"],
        category: "Edit",
        action: () => console.log("Find"),
      },
      {
        id: "replace",
        name: "Replace",
        description: "Find and replace in current file",
        shortcut: ["Ctrl", "H"],
        category: "Edit",
        action: () => console.log("Replace"),
      },
      {
        id: "toggle-sidebar",
        name: "Toggle Sidebar",
        description: "Show or hide the sidebar",
        shortcut: ["Ctrl", "B"],
        category: "View",
        action: () => console.log("Toggle sidebar"),
      },
      {
        id: "toggle-terminal",
        name: "Toggle Terminal",
        description: "Show or hide the terminal",
        shortcut: ["Ctrl", "`"],
        category: "View",
        action: () => console.log("Toggle terminal"),
      },
      {
        id: "toggle-ai-assistant",
        name: "Toggle AI Assistant",
        description: "Show or hide the AI assistant",
        category: "AI",
        action: () => console.log("Toggle AI assistant"),
      },
      {
        id: "generate-code",
        name: "Generate Code",
        description: "Generate code with AI",
        category: "AI",
        action: () => console.log("Generate code"),
      },
      {
        id: "explain-code",
        name: "Explain Code",
        description: "Explain selected code with AI",
        category: "AI",
        action: () => console.log("Explain code"),
      },
      {
        id: "optimize-code",
        name: "Optimize Code",
        description: "Optimize selected code with AI",
        category: "AI",
        action: () => console.log("Optimize code"),
      },
    ]

    setCommandItems(items)
    setFilteredItems(items)
  }, [])

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === "p") {
        e.preventDefault()
        setOpen(true)
      }

      if (e.key === "Escape" && open) {
        setOpen(false)
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [open])

  // ✅ Dialog Fix Logging
  useEffect(() => {
    if (open) {
      console.log('[🧩 DialogFix] Command Palette - Fixed positioning and scrolling');
    }
  }, [open])

  useEffect(() => {
    if (search) {
      setFilteredItems(
        commandItems.filter(
          (item) =>
            item.name.toLowerCase().includes(search.toLowerCase()) ||
            item.description.toLowerCase().includes(search.toLowerCase()) ||
            item.category.toLowerCase().includes(search.toLowerCase()),
        ),
      )
    } else {
      setFilteredItems(commandItems)
    }
  }, [search, commandItems])

  const executeCommand = (item: CommandItem) => {
    if (item.action) {
      item.action()
    }
    setOpen(false)
  }

  return (
    <>
      <Button
        variant="ghost"
        size="icon"
        className="h-8 w-8 text-muted-foreground hover:text-foreground"
        onClick={() => setOpen(true)}
      >
        <Command className="h-4 w-4" />
      </Button>

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="max-w-xl">
          <DialogHeader className="p-6 pb-0">
            <DialogTitle>Command Palette</DialogTitle>
          </DialogHeader>
          <div className="px-6">
            <Input
              className="w-full bg-background border focus-visible:ring-2 text-foreground placeholder-muted-foreground"
              placeholder="Search commands..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              autoFocus
            />
          </div>
          <ScrollArea className="max-h-[60vh]">
            <div className="p-6 pt-4">
              {filteredItems.length > 0 ? (
                Object.entries(
                  filteredItems.reduce<Record<string, CommandItem[]>>((acc, item) => {
                    if (!acc[item.category]) {
                      acc[item.category] = []
                    }
                    acc[item.category].push(item)
                    return acc
                  }, {}),
                ).map(([category, items]) => (
                  <div key={category} className="mb-4">
                    <div className="px-2 py-1 text-xs font-medium text-muted-foreground uppercase tracking-wider">
                      {category}
                    </div>
                    <div className="mt-1">
                      {items.map((item) => (
                        <div
                          key={item.id}
                          className="px-2 py-1.5 rounded-md hover:bg-accent hover:text-accent-foreground cursor-pointer"
                          onClick={() => executeCommand(item)}
                        >
                          <div className="flex items-center justify-between">
                            <div>
                              <div className="text-sm font-medium">{item.name}</div>
                              <div className="text-xs text-muted-foreground">{item.description}</div>
                            </div>
                            {item.shortcut && (
                              <div className="flex items-center space-x-1">
                                {item.shortcut.map((key, index) => (
                                  <span
                                    key={index}
                                    className="px-1.5 py-0.5 rounded bg-muted text-xs text-muted-foreground border border-border"
                                  >
                                    {key}
                                  </span>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))
              ) : (
                <div className="p-4 text-center text-muted-foreground">No commands found</div>
              )}
            </div>
          </ScrollArea>
        </DialogContent>
      </Dialog>
    </>
  )
}
