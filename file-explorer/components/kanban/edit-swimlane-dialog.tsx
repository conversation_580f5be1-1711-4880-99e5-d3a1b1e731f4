"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Swimlane } from "./board-context"
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"

interface EditSwimlaneDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onUpdateSwimlane: (id: string, title: string) => void
  swimlane: Swimlane | null
}

export function EditSwimlaneDialog({
  open,
  onOpenChange,
  onUpdateSwimlane,
  swimlane,
}: EditSwimlaneDialogProps) {
  const [title, setTitle] = useState("")

  useEffect(() => {
    if (swimlane) {
      setTitle(swimlane.title)
    }
  }, [swimlane])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (swimlane) {
      onUpdateSwimlane(swimlane.id, title)
    }
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[425px]">
        <DialogHeader className="p-6 pb-0">
          <DialogTitle>Edit Swimlane</DialogTitle>
        </DialogHeader>
        <ScrollArea className="max-h-[70vh]">
          <div className="p-6 pt-0">
            <form onSubmit={handleSubmit}>
              <div className="grid gap-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="title" className="text-right text-sm">
                    Title
                  </label>
                  <Input
                    id="title"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    className="col-span-3"
                    required
                  />
                </div>
              </div>
              <DialogFooter className="pt-4">
                <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                  Cancel
                </Button>
                <Button type="submit">Update Swimlane</Button>
              </DialogFooter>
            </form>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  )
}